comprehensive-pricing-table.tsx:1838 🚀 === SAVE ALL STARTED ===
comprehensive-pricing-table.tsx:1839 📊 Total pricing rows: 138
comprehensive-pricing-table.tsx:1840 📊 Modified rows: 138
comprehensive-pricing-table.tsx:1843 📊 Seasonal rows: 69
comprehensive-pricing-table.tsx:1848 📊 Base pricing rows: 69
comprehensive-pricing-table.tsx:1858 === SAVE ALL DEBUG START ===
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1873 Saving modified base pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, currency GBP
comprehensive-pricing-table.tsx:1944 [SAVE ALL] 💰 Base pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {row_info: {…}, display_values: {…}, api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:1980 === SEASONAL PRICING: 69 seasonal pricing rules to update ===
comprehensive-pricing-table.tsx:2003 🔍 DEDUPLICATION RESULTS:
comprehensive-pricing-table.tsx:2004 📊 Original seasonal rows: 69
comprehensive-pricing-table.tsx:2005 📊 Unique seasonal rows: 69
comprehensive-pricing-table.tsx:2006 📊 Removed duplicates: 0
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2029 ✨ Creating NEW group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer, groupKey: prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2029 ✨ Creating NEW group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer, groupKey: prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2029 ✨ Creating NEW group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2024 🔗 Grouping seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer, groupKey: prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2041 ♻️ Adding to EXISTING group for prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08
comprehensive-pricing-table.tsx:2114 🎯 FINAL SEASONAL PRICING GROUPS:
comprehensive-pricing-table.tsx:2115 📊 Total groups: 3
comprehensive-pricing-table.tsx:2120 📋 Group prod_01JWDR3C3VGQS7DBMJGARGS6D0_j2p2zkimzjb6r2lxgrej08: Room prod_01JWDR3C3VGQS7DBMJGARGS6D0, Season Summer, Rules: 23
comprehensive-pricing-table.tsx:2120 📋 Group prod_01JWDRAC203T1EHYTTDG8BV94R_j2p2zkimzjb6r2lxgrej08: Room prod_01JWDRAC203T1EHYTTDG8BV94R, Season Summer, Rules: 23
comprehensive-pricing-table.tsx:2120 📋 Group prod_01JYP0CWM0HC5H69FA44GR6S4V_j2p2zkimzjb6r2lxgrej08: Room prod_01JYP0CWM0HC5H69FA44GR6S4V, Season Summer, Rules: 23
comprehensive-pricing-table.tsx:2129 🌟 SEASONAL BULK API CALL 1/3: Updating seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer with 23 rules (first occurrence: true)
comprehensive-pricing-table.tsx:2172 [FRONTEND] 🌟 Seasonal pricing data for room prod_01JWDR3C3VGQS7DBMJGARGS6D0: {total_rules: 23, unique_combinations: 23, rule_combinations: Array(23), api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:2129 🌟 SEASONAL BULK API CALL 2/3: Updating seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer with 23 rules (first occurrence: false)
comprehensive-pricing-table.tsx:2172 [FRONTEND] 🌟 Seasonal pricing data for room prod_01JWDRAC203T1EHYTTDG8BV94R: {total_rules: 23, unique_combinations: 23, rule_combinations: Array(23), api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:2129 🌟 SEASONAL BULK API CALL 3/3: Updating seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer with 23 rules (first occurrence: false)
comprehensive-pricing-table.tsx:2172 [FRONTEND] 🌟 Seasonal pricing data for room prod_01JYP0CWM0HC5H69FA44GR6S4V: {total_rules: 23, unique_combinations: 23, rule_combinations: Array(23), api_payload: {…}, note: 'Backend handles currency conversion - sending display values directly'}
comprehensive-pricing-table.tsx:2223 ⏳ Waiting for 3 seasonal pricing API calls to complete...
comprehensive-pricing-table.tsx:2210 ✅ API CALL 3 COMPLETED: Successfully updated seasonal pricing for room prod_01JYP0CWM0HC5H69FA44GR6S4V, season Summer
comprehensive-pricing-table.tsx:2217 📊 API Response: {seasonal_rules: Array(23)}
comprehensive-pricing-table.tsx:2210 ✅ API CALL 2 COMPLETED: Successfully updated seasonal pricing for room prod_01JWDRAC203T1EHYTTDG8BV94R, season Summer
comprehensive-pricing-table.tsx:2217 📊 API Response: {seasonal_rules: Array(23)}
comprehensive-pricing-table.tsx:2210 ✅ API CALL 1 COMPLETED: Successfully updated seasonal pricing for room prod_01JWDR3C3VGQS7DBMJGARGS6D0, season Summer
comprehensive-pricing-table.tsx:2217 📊 API Response: {seasonal_rules: Array(23)}
comprehensive-pricing-table.tsx:2225 ✅ All 3 seasonal pricing API calls completed successfully!
comprehensive-pricing-table.tsx:2231 === BASE PRICING: 3 room configs to process ===
comprehensive-pricing-table.tsx:2240 💰 BASE API CALL: Saving base pricing for room config prod_01JWDR3C3VGQS7DBMJGARGS6D0 with 23 rules
comprehensive-pricing-table.tsx:2240 💰 BASE API CALL: Saving base pricing for room config prod_01JWDRAC203T1EHYTTDG8BV94R with 23 rules
comprehensive-pricing-table.tsx:2240 💰 BASE API CALL: Saving base pricing for room config prod_01JYP0CWM0HC5H69FA44GR6S4V with 23 rules
comprehensive-pricing-table.tsx:2281 === SAVE ALL DEBUG END: 3 seasonal + 3 base = 6 total API calls ===
use-admin-hotel-comprehensive-pricing.ts:127 [useAdminHotelComprehensivePricing] Raw API data: {hotel: {…}, room_configs: Array(3), occupancy_configs: Array(5), meal_plans: Array(6), room_pricing_data: Array(3)}
use-admin-hotel-comprehensive-pricing.ts:128 [useAdminHotelComprehensivePricing] Room configs: 3
use-admin-hotel-comprehensive-pricing.ts:132 [useAdminHotelComprehensivePricing] Room pricing data: 3
use-admin-hotel-comprehensive-pricing.ts:147 === RAW API DATA === {hotel: {…}, room_configs: Array(3), occupancy_configs: Array(5), meal_plans: Array(6), room_pricing_data: Array(3)}
use-admin-hotel-comprehensive-pricing.ts:127 [useAdminHotelComprehensivePricing] Raw API data: {hotel: {…}, room_configs: Array(3), occupancy_configs: Array(5), meal_plans: Array(6), room_pricing_data: Array(3)}
use-admin-hotel-comprehensive-pricing.ts:128 [useAdminHotelComprehensivePricing] Room configs: 3
use-admin-hotel-comprehensive-pricing.ts:132 [useAdminHotelComprehensivePricing] Room pricing data: 3
use-admin-hotel-comprehensive-pricing.ts:147 === RAW API DATA === {hotel: {…}, room_configs: Array(3), occupancy_configs: Array(5), meal_plans: Array(6), room_pricing_data: Array(3)}
